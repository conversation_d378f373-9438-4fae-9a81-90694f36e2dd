# Gemini-2.5-Flash 模型接入文档

## 概述

本文档描述了如何使用新接入的 Gemini-2.5-Flash 模型（基于 fal.ai nano-banana API）进行图像生成。

## 配置

### 1. 配置文件设置

在 `application.properties` 中添加以下配置：

```properties
# Gemini模型ID
geminiNanoBanana.modelId=gemini-nano-banana-001

# Gemini API配置
gemini.api.base-url=https://queue.fal.run
gemini.api.api-key=your_fal_api_key_here
gemini.api.default-timeout=300
gemini.api.max-retries=3
gemini.api.default-output-format=jpeg
gemini.api.default-num-images=1
gemini.api.max-concurrent-jobs=10
gemini.api.polling-interval-seconds=3
gemini.api.max-polling-attempts=20
gemini.api.first-delay-seconds=5
gemini.api.task-timeout-seconds=600
gemini.api.sync-mode-enabled=true
gemini.api.connect-timeout-seconds=30
gemini.api.read-timeout-seconds=60
gemini.api.write-timeout-seconds=60
```

### 2. API密钥获取

1. 访问 [fal.ai](https://fal.ai) 注册账号
2. 获取 API 密钥
3. 将密钥配置到 `gemini.api.api-key` 中

## API接口

### 1. 兼容接口（推荐）

使用现有的统一生图接口，通过设置 `model_id` 来调用 Gemini：

```http
POST /api/gen/create
Content-Type: application/json
Authorization: Bearer {token}

{
  "prompt": "A beautiful sunset over the ocean",
  "negative_prompt": "",
  "model_id": "gemini-nano-banana-001",
  "resolution": {
    "width": 1024,
    "height": 1024,
    "batch_size": 1
  }
}
```

### 2. 专用接口（测试用）

#### 创建任务
```http
POST /api/gemini/nano-banana
Content-Type: application/json
Authorization: Bearer {token}

{
  "prompt": "A cute cat sitting on a windowsill",
  "numImages": 1,
  "outputFormat": "jpeg",
  "syncMode": false
}
```

#### 获取任务结果
```http
GET /api/gemini/result/{requestId}
Authorization: Bearer {token}
```

#### 获取任务状态
```http
GET /api/gemini/status/{requestId}
Authorization: Bearer {token}
```

#### 同步生成（测试）
```http
POST /api/gemini/generate-sync
Content-Type: application/json
Authorization: Bearer {token}

{
  "prompt": "A futuristic cityscape at night",
  "numImages": 1,
  "outputFormat": "jpeg",
  "syncMode": true
}
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "markId": "gemini-12345678-1234-1234-1234-123456789abc",
    "requestId": "fal-request-id-12345",
    "index": 0,
    "fastHour": true,
    "featureName": "ttp"
  }
}
```

### 错误响应
```json
{
  "code": 4029,
  "message": "This image processing is failed. Please try again later"
}
```

## 错误码

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 4029 | GEMINI_API_ERROR | Gemini API调用失败 |
| 4030 | GEMINI_EXCEED_CONCURRENT_JOBS | 超过并发任务限制 |
| 4031 | GEMINI_PROMPT_REQUIRED | 提示词不能为空 |

## 特性

### 1. 并发控制
- 每个用户最多同时进行 10 个 Gemini 任务
- 超过限制时返回错误码 4030

### 2. 任务轮询
- 自动轮询任务状态，每 3 秒查询一次
- 最多轮询 20 次，超时后自动清理

### 3. 异步处理
- 支持异步任务处理
- 通过 WebSocket 或轮询接口获取结果

### 4. 图像处理
- 支持 JPEG 和 PNG 格式输出
- 自动压缩和云存储上传
- 集成现有的图像处理流程

## 测试

### 单元测试
```bash
mvn test -Dtest=GeminiControllerTest
```

### 手动测试
1. 配置 API 密钥
2. 启动应用
3. 使用 Postman 或 curl 调用接口
4. 检查返回结果和日志

## 注意事项

1. **API密钥安全**：请妥善保管 fal.ai API 密钥，不要提交到代码仓库
2. **并发限制**：注意 fal.ai 的 API 调用频率限制
3. **错误处理**：建议在客户端实现重试机制
4. **监控**：关注 Gemini 任务的成功率和响应时间

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查配置文件中的密钥是否正确
   - 确认密钥是否有效且未过期

2. **网络连接问题**
   - 检查服务器是否能访问 fal.ai
   - 确认防火墙设置

3. **任务超时**
   - 检查轮询配置是否合理
   - 查看 fal.ai API 状态

### 日志查看
```bash
# 查看 Gemini 相关日志
grep "Gemini" logs/application.log

# 查看错误日志
grep "ERROR.*Gemini" logs/application.log
```

## 更新日志

- **v1.0.0** (2024-01-XX): 初始版本，支持基础图像生成功能
