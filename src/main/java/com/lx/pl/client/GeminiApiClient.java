package com.lx.pl.client;

import com.lx.pl.dto.gemini.GeminiRequest;
import com.lx.pl.dto.gemini.GeminiResponse;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * Gemini API客户端接口
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
public interface GeminiApiClient {

    /**
     * 提交Nano Banana图像生成任务（异步）
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 提交任务响应
     */
    @POST("fal-ai/nano-banana")
    Call<GeminiResponse.SubmitTaskResponse> submitNanoBananaTask(
            @Header("Authorization") String apiKey,
            @Body GeminiRequest.NanoBananaRequest request
    );

    /**
     * 获取任务状态
     *
     * @param apiKey    API密钥
     * @param requestId 请求ID
     * @return 任务状态响应
     */
    @GET("fal-ai/nano-banana/requests/{requestId}/status")
    Call<GeminiResponse.TaskStatusResponse> getTaskStatus(
            @Header("Authorization") String apiKey,
            @Path("requestId") String requestId
    );

    /**
     * 获取任务结果
     *
     * @param apiKey    API密钥
     * @param requestId 请求ID
     * @return 任务结果响应
     */
    @GET("fal-ai/nano-banana/requests/{requestId}")
    Call<GeminiResponse.TaskStatusResponse> getTaskResult(
            @Header("Authorization") String apiKey,
            @Path("requestId") String requestId
    );

    /**
     * 直接调用Nano Banana图像生成（同步）
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 生成结果响应
     */
    @POST("fal-ai/nano-banana")
    Call<GeminiResponse.TaskStatusResponse> generateImageSync(
            @Header("Authorization") String apiKey,
            @Body GeminiRequest.NanoBananaRequest request
    );
}
