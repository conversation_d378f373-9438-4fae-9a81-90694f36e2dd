package com.lx.pl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Gemini API配置类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "gemini.api")
public class GeminiConfig {

    /**
     * fal.ai API的基础URL
     */
    private String baseUrl = "https://queue.fal.run";

    /**
     * fal.ai API密钥
     */
    private String apiKey;

    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeout = 300;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 默认输出格式
     */
    private String defaultOutputFormat = "jpeg";

    /**
     * 默认生成图片数量
     */
    private Integer defaultNumImages = 1;

    /**
     * 最大并发任务数（默认10个）
     */
    private Integer maxConcurrentJobs = 10;

    /**
     * 任务状态轮询间隔（秒，默认3秒）
     */
    private Integer pollingIntervalSeconds = 3;

    /**
     * 最大轮询次数（默认20次）
     */
    private Integer maxPollingAttempts = 20;

    /**
     * 首次轮询延迟时间（秒，默认5秒）
     */
    private Integer firstDelaySeconds = 5;

    /**
     * 任务超时时间（秒，默认600秒）
     */
    private Integer taskTimeoutSeconds = 600;

    /**
     * 是否启用同步模式
     */
    private Boolean syncModeEnabled = false;

    /**
     * 连接超时时间（秒）
     */
    private Integer connectTimeoutSeconds = 30;

    /**
     * 读取超时时间（秒）
     */
    private Integer readTimeoutSeconds = 60;

    /**
     * 写入超时时间（秒）
     */
    private Integer writeTimeoutSeconds = 60;
}
