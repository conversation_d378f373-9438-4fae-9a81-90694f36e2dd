package com.lx.pl.controller.gemini;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.config.GeminiConfig;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ModelInformation;
import com.lx.pl.dto.ModelRightsVerifyResult;
import com.lx.pl.dto.gemini.GeminiResponse;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.exception.LogicException;
import com.lx.pl.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Gemini控制器
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Gemini图像生成接口")
@RestController
@RequestMapping("/api/gemini")
public class GeminiController {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Autowired
    private GeminiService geminiService;

    @Autowired
    private GeminiConfig geminiConfig;

    @Autowired
    private GenService genService;

    @Autowired
    private VipService vipService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    @Resource
    private ModelService modelService;

    /**
     * Nano Banana图像生成请求参数
     */
    @Data
    public static class NanoBananaParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;

        private Integer numImages = 1; // 生成图片数量

        private String outputFormat = "jpeg"; // 输出格式

        private Boolean syncMode = false; // 同步模式
    }

    /**
     * 创建Gemini Nano Banana图像生成任务（非兼容接口，用于测试）
     */
    @PostMapping("/nano-banana")
    @Operation(summary = "Gemini Nano Banana图像生成")
    @Authorization
    public R<GeminiResponse.SubmitTaskResponse> createNanoBananaTask(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Valid NanoBananaParams params) {

        log.info("Gemini Nano Banana request from user: {}, prompt: {}",
                user.getLoginName(), params.getPrompt());

        // 检查并发任务数限制
        if (geminiService.checkGeminiConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.GEMINI_EXCEED_CONCURRENT_JOBS);
        }

        // 构建GenGenericPara参数
        GenGenericPara genParameters = new GenGenericPara();
        genParameters.setPrompt(params.getPrompt());

        GeminiResponse.SubmitTaskResponse result = geminiService.createNanoBananaTask(
                params.getPrompt(),
                user,
                null, // markId为空，不保存到数据库
                false, // fastHour
                "web", // platform
                genParameters,
                FeaturesType.ttp.getValue(),
                null // modelRightsVerifyResult
        );

        return R.success(result);
    }

    /**
     * 获取任务结果
     */
    @GetMapping("/result/{requestId}")
    @Operation(summary = "获取Gemini任务结果")
    @Authorization
    public R<GeminiResponse.TaskStatusResponse> getTaskResult(
            @Parameter(hidden = true) @CurrentUser User user,
            @PathVariable String requestId) {

        log.info("Get Gemini task result request from user: {}, requestId: {}",
                user.getLoginName(), requestId);

        GeminiResponse.TaskStatusResponse result = geminiService.getTaskResult(requestId);
        return R.success(result);
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status/{requestId}")
    @Operation(summary = "获取Gemini任务状态")
    @Authorization
    public R<GeminiResponse.TaskStatusResponse> getTaskStatus(
            @Parameter(hidden = true) @CurrentUser User user,
            @PathVariable String requestId) {

        log.info("Get Gemini task status request from user: {}, requestId: {}",
                user.getLoginName(), requestId);

        GeminiResponse.TaskStatusResponse result = geminiService.getTaskStatus(requestId);
        return R.success(result);
    }

    /**
     * 同步生成图像（测试接口）
     */
    @PostMapping("/generate-sync")
    @Operation(summary = "Gemini同步图像生成（测试）")
    @Authorization
    public R<GeminiResponse.TaskStatusResponse> generateImageSync(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Valid NanoBananaParams params) {

        log.info("Gemini sync generation request from user: {}, prompt: {}",
                user.getLoginName(), params.getPrompt());

        // 构建GenGenericPara参数
        GenGenericPara genParameters = new GenGenericPara();
        genParameters.setPrompt(params.getPrompt());

        GeminiResponse.TaskStatusResponse result = geminiService.generateImageSync(
                params.getPrompt(),
                genParameters
        );

        return R.success(result);
    }

    /**
     * 兼容原GenController的create接口
     */
    @PostMapping("/create")
    @Operation(summary = "Gemini图片生成（兼容原接口）")
    @Authorization
    public R<Map<String, Object>> createCompatible(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody GenGenericPara genParameters,
            HttpServletRequest request) throws IOException {

        // 基础参数验证
        if (genParameters.getPrompt() == null || genParameters.getPrompt().isEmpty()) {
            throw new LogicException(LogicErrorCode.GEMINI_PROMPT_REQUIRED);
        }

        // Prompt过滤验证
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        // 平台验证
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
        }

        // 是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
            }
        }

        if (null == genParameters.getNegative_prompt()) {
            genParameters.setNegative_prompt("");
        }
        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        if (null == genParameters.getResolution()) {
            return R.fail(400, "Image dimensions required !");
        }
        if (0 >= genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width required !");
        }
        if (0 >= genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height required !");
        }
        if (2048 < genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width exceeds 2048 pixels !");
        }
        if (2048 < genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height exceeds 2048 pixels !");
        }
        if (1 > genParameters.getResolution().getBatch_size()) {
            return R.fail(400, "At least one image required !");
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        Boolean fastHour = true;

        //校验点数
        if (!vipService.judgeUserFastCreate(modelRightsVerifyResult, user, genParameters.getResolution().getBatch_size(), Boolean.FALSE, OriginCreate.create, null, null)) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 设置传参
        String markId = LogicConstants.GEMINI_MARKID_PREFIX + UUID.randomUUID();
        if (genService.checkUserConcurrentJobs(user, markId, false)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查Gemini并发任务数限制
        if (geminiService.checkGeminiConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.GEMINI_EXCEED_CONCURRENT_JOBS);
        }

        String feature = FeaturesType.ttp.getValue();

        // 调用Gemini API
        GeminiResponse.SubmitTaskResponse geminiResult = geminiService.createNanoBananaTask(
                genParameters.getPrompt(),
                user,
                markId,
                fastHour,
                platform,
                genParameters,
                feature,
                modelRightsVerifyResult
        );

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("markId", markId);
        result.put("requestId", geminiResult.getRequestId());
        result.put("index", 0);
        result.put("fastHour", fastHour);
        result.put("featureName", feature);

        return R.success(result);
    }
}
