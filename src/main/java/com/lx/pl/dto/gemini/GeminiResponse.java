package com.lx.pl.dto.gemini;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Gemini API响应类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GeminiResponse {

    /**
     * 请求ID
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 任务状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 结果数据
     */
    @JsonProperty("data")
    private Object data;

    /**
     * 错误信息
     */
    @JsonProperty("error")
    private String error;

    /**
     * 详细信息
     */
    @JsonProperty("detail")
    private String detail;

    /**
     * 提交任务响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SubmitTaskResponse {
        /**
         * 请求ID
         */
        @JsonProperty("request_id")
        private String requestId;
    }

    /**
     * 任务结果数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskResult {

        /**
         * 生成的图像列表
         */
        @JsonProperty("images")
        private List<ImageInfo> images;

        /**
         * Gemini的文本描述或响应
         */
        @JsonProperty("description")
        private String description;
    }

    /**
     * 图像信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageInfo {
        /**
         * 图像URL
         */
        @JsonProperty("url")
        private String url;

        /**
         * 内容类型
         */
        @JsonProperty("content_type")
        private String contentType;

        /**
         * 文件名
         */
        @JsonProperty("file_name")
        private String fileName;

        /**
         * 文件大小
         */
        @JsonProperty("file_size")
        private Integer fileSize;
    }

    /**
     * 任务状态响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskStatusResponse {
        /**
         * 请求ID
         */
        @JsonProperty("request_id")
        private String requestId;

        /**
         * 任务状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 结果数据
         */
        @JsonProperty("data")
        private TaskResult data;

        /**
         * 错误信息
         */
        @JsonProperty("error")
        private String error;

        /**
         * 详细信息
         */
        @JsonProperty("detail")
        private String detail;
    }

    /**
     * 错误响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorResponse {
        /**
         * 错误代码
         */
        @JsonProperty("error")
        private String error;

        /**
         * 错误消息
         */
        @JsonProperty("detail")
        private String detail;

        /**
         * 详细信息
         */
        @JsonProperty("details")
        private Map<String, Object> details;
    }
}
