package com.lx.pl.exception;

import com.lx.pl.dto.gemini.GeminiResponse;
import lombok.Getter;

import java.util.List;

/**
 * Gemini API异常类
 * 用于处理fal.ai API返回的各种错误
 *
 * <AUTHOR>
 */
@Getter
public class GeminiApiException extends RuntimeException {

    /**
     * HTTP状态码
     */
    private final int statusCode;

    /**
     * 是否可重试
     */
    private final boolean retryable;

    /**
     * fal.ai错误详情
     */
    private final List<GeminiResponse.FalError> errors;

    /**
     * 错误类型
     */
    private final String errorType;

    public GeminiApiException(int statusCode, boolean retryable, List<GeminiResponse.FalError> errors) {
        super(buildMessage(errors));
        this.statusCode = statusCode;
        this.retryable = retryable;
        this.errors = errors;
        this.errorType = extractPrimaryErrorType(errors);
    }

    public GeminiApiException(int statusCode, String message) {
        super(message);
        this.statusCode = statusCode;
        this.retryable = isRetryableByStatusCode(statusCode);
        this.errors = null;
        this.errorType = "unknown";
    }

    /**
     * 构建错误消息
     */
    private static String buildMessage(List<GeminiResponse.FalError> errors) {
        if (errors == null || errors.isEmpty()) {
            return "Unknown Gemini API error";
        }

        GeminiResponse.FalError primaryError = errors.get(0);
        return String.format("Gemini API Error [%s]: %s",
                primaryError.getType(), primaryError.getMsg());
    }

    /**
     * 提取主要错误类型
     */
    private static String extractPrimaryErrorType(List<GeminiResponse.FalError> errors) {
        if (errors == null || errors.isEmpty()) {
            return "unknown";
        }
        return errors.get(0).getType();
    }

    /**
     * 根据状态码判断是否可重试
     */
    private static boolean isRetryableByStatusCode(int statusCode) {
        return statusCode == 500 || statusCode == 502 || statusCode == 503 || statusCode == 504;
    }

    /**
     * 检查是否为内容违规错误
     */
    public boolean isContentPolicyViolation() {
        return "content_policy_violation".equals(errorType);
    }

    /**
     * 检查是否为图片相关错误
     */
    public boolean isImageError() {
        return errorType != null && (
                errorType.startsWith("image_") ||
                        errorType.equals("unsupported_image_format")
        );
    }

    /**
     * 检查是否为验证错误
     */
    public boolean isValidationError() {
        return errorType != null && (
                errorType.equals("greater_than") ||
                        errorType.equals("less_than") ||
                        errorType.equals("greater_than_equal") ||
                        errorType.equals("less_than_equal") ||
                        errorType.equals("multiple_of") ||
                        errorType.equals("one_of") ||
                        errorType.equals("sequence_too_short") ||
                        errorType.equals("sequence_too_long")
        );
    }

    /**
     * 检查是否为服务不可用错误
     */
    public boolean isServiceUnavailable() {
        return "downstream_service_unavailable".equals(errorType) ||
                "internal_server_error".equals(errorType);
    }

    /**
     * 检查是否为超时错误
     */
    public boolean isTimeout() {
        return "generation_timeout".equals(errorType);
    }

    /**
     * 获取用户友好的错误消息
     */
    public String getUserFriendlyMessage() {
        if (isContentPolicyViolation()) {
            return "您的内容可能包含不当信息，请修改后重试";
        }
        
        if (isImageError()) {
            return "图片格式或尺寸不符合要求，请检查后重试";
        }

        if (isValidationError()) {
            return "请求参数不正确，请检查输入参数";
        }

        if (isServiceUnavailable()) {
            return "服务暂时不可用，请稍后重试";
        }

        if (isTimeout()) {
            return "生成超时，请稍后重试";
        }

        if (retryable) {
            return "服务暂时繁忙，请稍后重试";
        }

        return "生成失败，请检查输入后重试";
    }

    /**
     * 获取对应的LogicErrorCode
     */
    public String getLogicErrorCode() {
        if (isContentPolicyViolation()) {
            return "ILLEGAL_PROMPT";
        }

        if (isValidationError() || isImageError()) {
            return "INVALID_PARAMETER";
        }

        if (isServiceUnavailable() || isTimeout()) {
            return "GEMINI_API_ERROR";
        }

        return "GEMINI_API_ERROR";
    }
}
