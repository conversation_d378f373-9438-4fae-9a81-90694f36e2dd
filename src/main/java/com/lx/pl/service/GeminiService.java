package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.client.GeminiApiClient;
import com.lx.pl.config.GeminiConfig;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ModelRightsVerifyResult;
import com.lx.pl.dto.gemini.GeminiRequest;
import com.lx.pl.dto.gemini.GeminiResponse;
import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.enums.TaskTypeForMq;
import com.lx.pl.exception.LogicException;
import com.lx.pl.mq.producer.NormalMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Gemini服务类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GeminiService {

    @Autowired
    private GeminiApiClient geminiApiClient;

    @Autowired
    private GeminiConfig geminiConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Autowired
    private SendPollingService unifiedTaskPollingService;

    @Autowired
    private LumenService lumenService;

    @Autowired
    private GenService genService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${rocketmq.image.process.tag}")
    private String mjImageProcessTag;
    @Value("${rocketmq.piclumen.topic}")
    private String mjImageProcessTopic;

    // Redis key常量
    private static final String GEMINI_TASK_LOCK = "gemini_task_";
    public static final String GEMINI_TASK_PREFIX = "gemini:task:";
    private static final String GEMINI_USER_CONCURRENT_PREFIX = "gemini:concurrent";
    private static final String GEMINI_IMG_PREFIX = "gemini:do_img:";

    /**
     * 创建Nano Banana图像生成任务
     *
     * @param prompt        提示词
     * @param user          用户
     * @param markId        标记ID
     * @param fastHour      是否快速生图
     * @param platform      平台
     * @param genParameters 原始生图参数
     * @param feature       功能类型
     * @param modelRightsVerifyResult 模型权益验证结果
     * @return 任务响应
     */
    public GeminiResponse.SubmitTaskResponse createNanoBananaTask(String prompt, User user,
                                                                  String markId, Boolean fastHour, String platform, 
                                                                  GenGenericPara genParameters, String feature,
                                                                  ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            // 构建请求
            GeminiRequest.NanoBananaRequest request = new GeminiRequest.NanoBananaRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(geminiConfig.getDefaultOutputFormat());
            request.setNumImages(geminiConfig.getDefaultNumImages());
            request.setSyncMode(geminiConfig.getSyncModeEnabled());

            log.info("Creating Gemini Nano Banana task for user: {}, prompt: {}", 
                    user.getLoginName(), prompt);

            // 调用API
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.SubmitTaskResponse> response = geminiApiClient
                    .submitNanoBananaTask(authHeader, request)
                    .execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Gemini API call failed: {}, error: {}", response.code(), errorBody);
                throw new LogicException("GEMINI_API_ERROR", "Gemini API调用失败: " + errorBody);
            }

            GeminiResponse.SubmitTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getRequestId())) {
                log.error("Gemini API returned null response or empty request ID");
                throw new LogicException("GEMINI_API_ERROR", "Gemini API返回空响应");
            }

            // 添加到并发任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getRequestId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getRequestId(), prompt, user, markId, fastHour, platform, genParameters, feature, modelRightsVerifyResult);

                // 保存Gemini任务状态到Redis
                saveGeminiTaskStatusToRedis(responseData.getRequestId(), markId, user.getLoginName());

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getRequestId(), user.getLoginName(), geminiConfig.getFirstDelaySeconds());
            }

            log.info("Gemini Nano Banana task created successfully, requestId: {}", responseData.getRequestId());
            return responseData;

        } catch (IOException e) {
            log.error("Gemini API call failed with IOException", e);
            throw new LogicException("GEMINI_API_ERROR", "Gemini API调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during Gemini task creation", e);
            throw new LogicException("GEMINI_API_ERROR", "创建Gemini任务时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 检查Gemini并发任务数限制
     */
    public boolean checkGeminiConcurrentJobs(User user, String excludeTaskId) {
        String lockKey = GEMINI_TASK_LOCK + user.getLoginName();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Gemini任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredGeminiTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制
            int maxConcurrentJobs = geminiConfig.getMaxConcurrentJobs() != null ?
                    geminiConfig.getMaxConcurrentJobs() : 10;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Gemini concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            return false; // 未超过限制

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的Gemini任务
     */
    private void cleanExpiredGeminiTasks(String userConcurrentKey, List<String> taskList) {
        long currentTime = System.currentTimeMillis();
        long expireTime = geminiConfig.getTaskTimeoutSeconds() != null ?
                geminiConfig.getTaskTimeoutSeconds() * 1000L : 600000L; // 默认10分钟

        for (String taskId : taskList) {
            try {
                String timestampStr = redisService.getDataFromHash(userConcurrentKey, taskId);
                if (StringUtil.isNotBlank(timestampStr)) {
                    long timestamp = Long.parseLong(timestampStr);
                    if (currentTime - timestamp > expireTime) {
                        redisService.removeDataFromHash(userConcurrentKey, taskId);
                        log.info("Cleaned expired Gemini task: {}", taskId);
                    }
                }
            } catch (Exception e) {
                log.warn("Error cleaning expired Gemini task: {}", taskId, e);
                redisService.removeDataFromHash(userConcurrentKey, taskId);
            }
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String requestId, String prompt, User user, String markId, Boolean fastHour,
                                  String platform, GenGenericPara genParameters, String feature,
                                  ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setPromptId(requestId);
            promptRecord.setMarkId(markId);
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(genParameters.getNegative_prompt());
            promptRecord.setModelId(genParameters.getModel_id());
            promptRecord.setWidth(genParameters.getResolution().getWidth());
            promptRecord.setHeight(genParameters.getResolution().getHeight());
            promptRecord.setBatchSize(genParameters.getResolution().getBatch_size());
            promptRecord.setFastHour(fastHour);
            promptRecord.setPlatform(platform);
            promptRecord.setFeatureName(feature);
            promptRecord.setCreateTimestamp(LocalDateTime.now());
            promptRecord.setOriginCreate("create");

            // 设置点数消耗
            if (modelRightsVerifyResult != null) {
                promptRecord.setCostLumen(modelRightsVerifyResult.getCostLumen());
            }

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Gemini task: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to save PromptRecord for Gemini task: {}", requestId, e);
        }
    }

    /**
     * 保存Gemini任务状态到Redis
     */
    private void saveGeminiTaskStatusToRedis(String requestId, String markId, String loginName) {
        try {
            String taskKey = GEMINI_TASK_PREFIX + requestId;
            redisService.putDataToHash(taskKey, "markId", markId);
            redisService.putDataToHash(taskKey, "loginName", loginName);
            redisService.putDataToHash(taskKey, "status", "PENDING");
            redisService.putDataToHash(taskKey, "createTime", String.valueOf(System.currentTimeMillis()));
            
            // 设置过期时间
            redisService.expire(taskKey, geminiConfig.getTaskTimeoutSeconds() != null ? 
                    geminiConfig.getTaskTimeoutSeconds() : 600);

            log.info("Saved Gemini task status to Redis: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to save Gemini task status to Redis: {}", requestId, e);
        }
    }

    /**
     * 启动任务状态轮询（使用统一轮询服务）
     */
    private void startTaskStatusPolling(String requestId, String loginName, Integer delaySeconds) {
        log.info("Starting Gemini task status polling for requestId: {}, user: {}", requestId, loginName);

        int maxAttempts = geminiConfig.getMaxPollingAttempts() != null ?
                geminiConfig.getMaxPollingAttempts() : 20;
        int pollingInterval = geminiConfig.getPollingIntervalSeconds() != null ?
                geminiConfig.getPollingIntervalSeconds() * 1000 : 3000; // 转换为毫秒

        // 创建轮询消息
        TaskPollingVo pollingVo = new TaskPollingVo(
                TaskTypeForMq.GEMINI.getType(),
                requestId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis(),
                null // Gemini不需要pollingUrl
        );

        try {
            // 发送延迟消息到统一轮询服务
            unifiedTaskPollingService.sendDelayPollingMessage(pollingVo, delaySeconds);
            log.info("Sent Gemini polling message for requestId: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to send Gemini polling message for requestId: {}", requestId, e);
        }
    }
}
