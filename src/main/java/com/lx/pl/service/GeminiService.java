package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.client.GeminiApiClient;
import com.lx.pl.config.GeminiConfig;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ModelRightsVerifyResult;
import com.lx.pl.dto.gemini.GeminiRequest;
import com.lx.pl.dto.gemini.GeminiResponse;
import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.TaskTypeForMq;
import com.lx.pl.exception.LogicException;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

/**
 * Gemini服务类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GeminiService {

    @Autowired
    private GeminiApiClient geminiApiClient;

    @Autowired
    private GeminiConfig geminiConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Autowired
    private SendPollingService sendPollingService;

    @Autowired
    private LumenService lumenService;


    @Value("${rocketmq.image.process.tag}")
    private String mjImageProcessTag;
    @Value("${rocketmq.piclumen.topic}")
    private String mjImageProcessTopic;

    // Redis key常量
    private static final String GEMINI_TASK_LOCK = "gemini_task_";
    public static final String GEMINI_TASK_PREFIX = "gemini:task:";
    private static final String GEMINI_USER_CONCURRENT_PREFIX = "gemini:concurrent";
    private static final String GEMINI_IMG_PREFIX = "gemini:do_img:";

    /**
     * 创建Nano Banana图像生成任务
     *
     * @param prompt                  提示词
     * @param user                    用户
     * @param markId                  标记ID
     * @param fastHour                是否快速生图
     * @param platform                平台
     * @param genParameters           原始生图参数
     * @param feature                 功能类型
     * @param modelRightsVerifyResult 模型权益验证结果
     * @return 任务响应
     */
    public GeminiResponse.SubmitTaskResponse createNanoBananaTask(String prompt, User user,
                                                                  String markId, Boolean fastHour, String platform,
                                                                  GenGenericPara genParameters, String feature,
                                                                  ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            // 构建请求
            GeminiRequest.NanoBananaRequest request = new GeminiRequest.NanoBananaRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(geminiConfig.getDefaultOutputFormat());
            request.setNumImages(geminiConfig.getDefaultNumImages());
            request.setSyncMode(geminiConfig.getSyncModeEnabled());

            log.info("Creating Gemini Nano Banana task for user: {}, prompt: {}",
                    user.getLoginName(), prompt);

            // 调用API
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.SubmitTaskResponse> response = geminiApiClient
                    .submitNanoBananaTask(authHeader, request)
                    .execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Gemini API call failed: {}, error: {}", response.code(), errorBody);
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini API调用失败: " + errorBody);
            }

            GeminiResponse.SubmitTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getRequestId())) {
                log.error("Gemini API returned null response or empty request ID");
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini API返回空响应");
            }

            // 添加到并发任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX + ":" + user.getLoginName();
            redisService.putDataToHash(userConcurrentKey, responseData.getRequestId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getRequestId(), prompt, user, markId, fastHour, platform, genParameters, feature, modelRightsVerifyResult);

                // 保存Gemini任务状态到Redis
                saveGeminiTaskStatusToRedis(responseData.getRequestId(), markId, user.getLoginName());

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getRequestId(), user.getLoginName(), geminiConfig.getFirstDelaySeconds());
            }

            log.info("Gemini Nano Banana task created successfully, requestId: {}", responseData.getRequestId());
            return responseData;

        } catch (IOException e) {
            log.error("Gemini API call failed with IOException", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini API调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during Gemini task creation", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "创建Gemini任务时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 检查Gemini并发任务数限制
     */
    public boolean checkGeminiConcurrentJobs(User user, String excludeTaskId) {
        String lockKey = GEMINI_TASK_LOCK + user.getLoginName();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Gemini任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX + ":" + user.getLoginName();
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredGeminiTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制
            int maxConcurrentJobs = geminiConfig.getMaxConcurrentJobs() != null ?
                    geminiConfig.getMaxConcurrentJobs() : 10;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Gemini concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            return false; // 未超过限制

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的Gemini任务
     */
    private void cleanExpiredGeminiTasks(String userConcurrentKey, List<String> taskList) {
        long currentTime = System.currentTimeMillis();
        long expireTime = geminiConfig.getTaskTimeoutSeconds() != null ?
                geminiConfig.getTaskTimeoutSeconds() * 1000L : 600000L; // 默认10分钟

        for (String taskId : taskList) {
            try {
                String timestampStr = redisService.getDataFromHash(userConcurrentKey, taskId).toString();
                if (StringUtil.isNotBlank(timestampStr)) {
                    long timestamp = Long.parseLong(timestampStr);
                    if (currentTime - timestamp > expireTime) {
                        redisService.deleteFieldFromHash(userConcurrentKey, taskId);
                        log.info("Cleaned expired Gemini task: {}", taskId);
                    }
                }
            } catch (Exception e) {
                log.warn("Error cleaning expired Gemini task: {}", taskId, e);
                redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            }
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String requestId, String prompt, User user, String markId, Boolean fastHour,
                                  String platform, GenGenericPara genParameters, String feature,
                                  ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setPromptId(requestId);
            promptRecord.setMarkId(markId);
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(genParameters.getNegative_prompt());
            promptRecord.setModelId(genParameters.getModel_id());
            promptRecord.setBatchSize(genParameters.getResolution().getBatch_size());
            promptRecord.setFastHour(fastHour);
            promptRecord.setPlatform(platform);
            promptRecord.setFeatureName(feature);
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setOriginCreate("create");

            // 设置点数消耗
            if (modelRightsVerifyResult != null) {
                promptRecord.setCostLumens(modelRightsVerifyResult.getCostLumen());
            }
            promptRecord.setGenStartTime(LocalDateTime.now());
            if (FeaturesType.ttp.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.create.getValue());
            } else if (FeaturesType.edit.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.edit.getValue());
            } else if (FeaturesType.ptpGemini.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.picCreate.getValue());
            }
            // 如果有原始参数，保存为genInfo
            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            }

            // 设置宽高比，根据前端SHAPE_ALL配置处理
            String aspectRatio = "1024 x 1024"; // 默认值
            if (genParameters instanceof GenGenericPara) {
                GenGenericPara genPara = (GenGenericPara) genParameters;
                aspectRatio = AspectRatioUtils.getAspectRatio(genPara);
            }
            promptRecord.setAspectRatio(aspectRatio);
            promptRecord.setFeatureName(feature);
            promptRecord.setFastHour(fastHour != null ? fastHour : true);
            promptRecord.setPlatform(platform);
            promptRecord.setCostLumens(modelRightsVerifyResult.getCostLumen());
            promptRecord.setUseFreeTrial(modelRightsVerifyResult.isUseFreeTrial());
            promptRecord.setUsePayTrial(modelRightsVerifyResult.isUsePayTrial());

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Gemini task: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to save PromptRecord for Gemini task: {}", requestId, e);
        }
    }

    /**
     * 保存Gemini任务状态到Redis
     */
    private void saveGeminiTaskStatusToRedis(String taskId, String markId, String loginName) {
        try {
            // 1. 保存taskId -> markId的映射
            redisService.stringSet(taskId, markId, 2, TimeUnit.HOURS);

            // 2. 保存markId -> loginName的映射
            redisService.stringSet(markId, loginName, 2, TimeUnit.HOURS);

            // 3. 在用户hash中设置任务状态为0（直接进入开始执行状态）
            redisService.putDataToHash(loginName, markId, 0, 2, TimeUnit.HOURS);

            // 4. 设置任务时间戳
            redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);

            String taskKey = GEMINI_TASK_PREFIX + taskId;
            redisService.stringSet(taskKey, markId, 10, TimeUnit.MINUTES);

            // 预扣
            lumenService.notFinishTask(loginName);

            log.info("Saved Gemini task status to Redis: {}", taskId);

        } catch (Exception e) {
            log.error("Failed to save Gemini task status to Redis: {}", taskId, e);
        }
    }

    /**
     * 启动任务状态轮询（使用统一轮询服务）
     */
    private void startTaskStatusPolling(String requestId, String loginName, Integer delaySeconds) {
        log.info("Starting Gemini task status polling for requestId: {}, user: {}", requestId, loginName);

        int maxAttempts = geminiConfig.getMaxPollingAttempts() != null ?
                geminiConfig.getMaxPollingAttempts() : 20;
        int pollingInterval = geminiConfig.getPollingIntervalSeconds() != null ?
                geminiConfig.getPollingIntervalSeconds() * 1000 : 3000; // 转换为毫秒

        // 创建轮询消息
        TaskPollingVo pollingVo = new TaskPollingVo(
                TaskTypeForMq.GEMINI.getType(),
                requestId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis(),
                null // Gemini不需要pollingUrl
        );

        try {
            // 发送延迟消息到统一轮询服务
            sendPollingService.sendPollingMessage(pollingVo, delaySeconds);
            log.info("Sent Gemini polling message for requestId: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to send Gemini polling message for requestId: {}", requestId, e);
        }
    }

    /**
     * 获取任务结果
     */
    public GeminiResponse.TaskStatusResponse getTaskResult(String requestId) {
        try {
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.TaskStatusResponse> response = geminiApiClient
                    .getTaskResult(authHeader, requestId)
                    .execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Failed to get Gemini task result: {}, error: {}", response.code(), errorBody);
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "获取Gemini任务结果失败: " + errorBody);
            }

            return response.body();

        } catch (IOException e) {
            log.error("IOException while getting Gemini task result: {}", requestId, e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "获取Gemini任务结果时发生IO错误: " + e.getMessage());
        }
    }

    /**
     * 获取任务状态
     */
    public GeminiResponse.TaskStatusResponse getTaskStatus(String requestId) {
        try {
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.TaskStatusResponse> response = geminiApiClient
                    .getTaskStatus(authHeader, requestId)
                    .execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Failed to get Gemini task status: {}, error: {}", response.code(), errorBody);
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "获取Gemini任务状态失败: " + errorBody);
            }

            return response.body();

        } catch (IOException e) {
            log.error("IOException while getting Gemini task status: {}", requestId, e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "获取Gemini任务状态时发生IO错误: " + e.getMessage());
        }
    }

    /**
     * 同步生成图像（直接调用API）
     */
    public GeminiResponse.TaskStatusResponse generateImageSync(String prompt, GenGenericPara genParameters) {
        try {
            // 构建请求
            GeminiRequest.NanoBananaRequest request = new GeminiRequest.NanoBananaRequest();
            request.setPrompt(prompt);
            request.setOutputFormat(geminiConfig.getDefaultOutputFormat());
            request.setNumImages(geminiConfig.getDefaultNumImages());
            request.setSyncMode(true); // 同步模式

            log.info("Generating Gemini image synchronously, prompt: {}", prompt);

            // 调用API
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.TaskStatusResponse> response = geminiApiClient
                    .generateImageSync(authHeader, request)
                    .execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Gemini sync API call failed: {}, error: {}", response.code(), errorBody);
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini同步API调用失败: " + errorBody);
            }

            GeminiResponse.TaskStatusResponse responseData = response.body();
            if (responseData == null) {
                log.error("Gemini sync API returned null response");
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini同步API返回空响应");
            }

            log.info("Gemini image generated successfully synchronously");
            return responseData;

        } catch (IOException e) {
            log.error("Gemini sync API call failed with IOException", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini同步API调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during Gemini sync image generation", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini同步生图时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 清理任务从Redis
     */
    public void cleanupTaskFromRedis(String requestId, String loginName) {
        try {
            // 从并发任务列表中移除
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX + ":" + loginName;
            redisService.deleteFieldFromHash(userConcurrentKey, requestId);

            // 删除任务状态
            String taskKey = GEMINI_TASK_PREFIX + requestId;
            redisService.delete(taskKey);

            log.info("Cleaned up Gemini task from Redis: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to cleanup Gemini task from Redis: {}", requestId, e);
        }
    }
}
