package com.lx.pl.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.dto.gemini.GeminiResponse;
import com.lx.pl.exception.GeminiApiException;
import lombok.extern.slf4j.Slf4j;
import retrofit2.Response;

import java.util.List;

/**
 * Gemini API错误解析工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class GeminiErrorParser {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 解析Retrofit响应中的错误
     */
    public static GeminiApiException parseError(Response<?> response) {
        int statusCode = response.code();
        boolean retryable = isRetryableFromHeaders(response);

        try {
            if (response.errorBody() != null) {
                String errorBodyString = response.errorBody().string();
                log.debug("Gemini API error response: {}", errorBodyString);

                // 尝试解析fal.ai标准错误格式
                try {
                    GeminiResponse.FalErrorResponse falErrorResponse = 
                            objectMapper.readValue(errorBodyString, GeminiResponse.FalErrorResponse.class);
                    
                    if (falErrorResponse.getDetail() != null && !falErrorResponse.getDetail().isEmpty()) {
                        return new GeminiApiException(statusCode, retryable, falErrorResponse.getDetail());
                    }
                } catch (Exception e) {
                    log.debug("Failed to parse as fal.ai error format, trying fallback", e);
                }

                // 回退到简单错误消息
                return new GeminiApiException(statusCode, "Gemini API Error: " + errorBodyString);
            }

            return new GeminiApiException(statusCode, "Gemini API Error: HTTP " + statusCode);

        } catch (Exception e) {
            log.error("Failed to parse Gemini API error", e);
            return new GeminiApiException(statusCode, "Failed to parse Gemini API error");
        }
    }

    /**
     * 从响应头中判断是否可重试
     */
    private static boolean isRetryableFromHeaders(Response<?> response) {
        String retryableHeader = response.headers().get("X-Fal-Retryable");
        if (retryableHeader != null) {
            return "true".equalsIgnoreCase(retryableHeader);
        }

        // 根据状态码判断
        int statusCode = response.code();
        return statusCode == 500 || statusCode == 502 || statusCode == 503 || statusCode == 504;
    }

    /**
     * 检查错误是否为特定类型
     */
    public static boolean isErrorType(GeminiApiException exception, String errorType) {
        return errorType.equals(exception.getErrorType());
    }

    /**
     * 检查错误是否为任意一种类型
     */
    public static boolean isAnyErrorType(GeminiApiException exception, String... errorTypes) {
        String actualType = exception.getErrorType();
        for (String errorType : errorTypes) {
            if (errorType.equals(actualType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从错误中提取上下文信息
     */
    public static Object getErrorContext(GeminiApiException exception, String key) {
        if (exception.getErrors() == null || exception.getErrors().isEmpty()) {
            return null;
        }

        GeminiResponse.FalError primaryError = exception.getErrors().get(0);
        if (primaryError.getCtx() == null) {
            return null;
        }

        return primaryError.getCtx().get(key);
    }

    /**
     * 获取错误的输入值
     */
    public static Object getErrorInput(GeminiApiException exception) {
        if (exception.getErrors() == null || exception.getErrors().isEmpty()) {
            return null;
        }

        return exception.getErrors().get(0).getInput();
    }

    /**
     * 获取错误的位置信息
     */
    public static List<String> getErrorLocation(GeminiApiException exception) {
        if (exception.getErrors() == null || exception.getErrors().isEmpty()) {
            return null;
        }

        return exception.getErrors().get(0).getLoc();
    }

    /**
     * 构建详细的错误日志信息
     */
    public static String buildDetailedErrorLog(GeminiApiException exception) {
        StringBuilder sb = new StringBuilder();
        sb.append("Gemini API Error Details:\n");
        sb.append("  Status Code: ").append(exception.getStatusCode()).append("\n");
        sb.append("  Error Type: ").append(exception.getErrorType()).append("\n");
        sb.append("  Retryable: ").append(exception.isRetryable()).append("\n");

        if (exception.getErrors() != null) {
            for (int i = 0; i < exception.getErrors().size(); i++) {
                GeminiResponse.FalError error = exception.getErrors().get(i);
                sb.append("  Error ").append(i + 1).append(":\n");
                sb.append("    Location: ").append(error.getLoc()).append("\n");
                sb.append("    Message: ").append(error.getMsg()).append("\n");
                sb.append("    Type: ").append(error.getType()).append("\n");
                if (error.getCtx() != null) {
                    sb.append("    Context: ").append(error.getCtx()).append("\n");
                }
                if (error.getInput() != null) {
                    sb.append("    Input: ").append(error.getInput()).append("\n");
                }
            }
        }

        return sb.toString();
    }
}
