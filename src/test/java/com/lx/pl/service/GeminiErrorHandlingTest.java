package com.lx.pl.service;

import com.lx.pl.exception.GeminiApiException;
import com.lx.pl.util.GeminiErrorParser;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Gemini错误处理测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class GeminiErrorHandlingTest {

    @Test
    public void testContentPolicyViolationError() {
        // 模拟内容违规错误
        GeminiApiException exception = new GeminiApiException(422, false, null);
        
        // 测试错误类型判断
        assertTrue(exception.isContentPolicyViolation());
        assertFalse(exception.isRetryable());
        
        // 测试用户友好消息
        String userMessage = exception.getUserFriendlyMessage();
        assertTrue(userMessage.contains("不当信息"));
        
        // 测试LogicErrorCode映射
        assertEquals("ILLEGAL_PROMPT", exception.getLogicErrorCode());
    }

    @Test
    public void testImageTooLargeError() {
        // 模拟图片过大错误
        GeminiApiException exception = new GeminiApiException(422, false, null);
        
        assertTrue(exception.isImageError());
        assertTrue(exception.isValidationError());
        assertFalse(exception.isRetryable());
        
        String userMessage = exception.getUserFriendlyMessage();
        assertTrue(userMessage.contains("图片格式") || userMessage.contains("尺寸"));
        
        assertEquals("INVALID_PARAMETER", exception.getLogicErrorCode());
    }

    @Test
    public void testInternalServerError() {
        // 模拟服务器内部错误
        GeminiApiException exception = new GeminiApiException(500, true, null);
        
        assertTrue(exception.isServiceUnavailable());
        assertTrue(exception.isRetryable());
        
        String userMessage = exception.getUserFriendlyMessage();
        assertTrue(userMessage.contains("不可用") || userMessage.contains("重试"));
        
        assertEquals("GEMINI_API_ERROR", exception.getLogicErrorCode());
    }

    @Test
    public void testGenerationTimeoutError() {
        // 模拟生成超时错误
        GeminiApiException exception = new GeminiApiException(504, true, null);
        
        assertTrue(exception.isTimeout());
        assertTrue(exception.isRetryable());
        
        String userMessage = exception.getUserFriendlyMessage();
        assertTrue(userMessage.contains("超时"));
        
        assertEquals("GEMINI_API_ERROR", exception.getLogicErrorCode());
    }

    @Test
    public void testValidationError() {
        // 模拟验证错误
        GeminiApiException exception = new GeminiApiException(422, false, null);
        
        assertTrue(exception.isValidationError());
        assertFalse(exception.isRetryable());
        
        String userMessage = exception.getUserFriendlyMessage();
        assertTrue(userMessage.contains("参数"));
        
        assertEquals("INVALID_PARAMETER", exception.getLogicErrorCode());
    }

    @Test
    public void testRetryableByStatusCode() {
        // 测试根据状态码判断是否可重试
        assertTrue(new GeminiApiException(500, "Server Error").isRetryable());
        assertTrue(new GeminiApiException(502, "Bad Gateway").isRetryable());
        assertTrue(new GeminiApiException(503, "Service Unavailable").isRetryable());
        assertTrue(new GeminiApiException(504, "Gateway Timeout").isRetryable());
        
        assertFalse(new GeminiApiException(400, "Bad Request").isRetryable());
        assertFalse(new GeminiApiException(422, "Validation Error").isRetryable());
    }

    @Test
    public void testErrorMessageBuilding() {
        // 测试错误消息构建
        GeminiApiException exception = new GeminiApiException(422, "Test error message");
        
        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Test error message"));
        
        assertNotNull(exception.getUserFriendlyMessage());
        assertFalse(exception.getUserFriendlyMessage().isEmpty());
    }
}
